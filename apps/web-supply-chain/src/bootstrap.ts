import { createApp, watchEffect } from 'vue';

import { registerAccessDirective } from '@vben/access';
import { initTippy, registerLoadingDirective } from '@vben/common-ui';
import { MotionPlugin } from '@vben/plugins/motion';
import { preferences } from '@vben/preferences';
import { initStores, useDictStore, useSystemConfigStore } from '@vben/stores';
import '@vben/styles';
import '@vben/styles/antd';

import { useTitle } from '@vueuse/core';

import { setupAntd } from '#/adapter/antd';
import { getDictListApi, getPublicConfigInfoApi } from '#/api';
// import { setupGlobDirectives } from '#/directives';
import { $t, setupI18n } from '#/locales';

import { initComponentAdapter } from './adapter/component';
import { initSetupVbenForm } from './adapter/form';
import App from './app.vue';
import { router } from './router';

// import '#/style/common.less';

async function bootstrap(namespace: string) {
  // 初始化组件适配器
  await initComponentAdapter();
  // 初始化表单组件
  await initSetupVbenForm();

  // // 设置弹窗的默认配置
  // setDefaultModalProps({
  //   fullscreenButton: false,
  // });
  // // 设置抽屉的默认配置
  // setDefaultDrawerProps({
  //   zIndex: 1020,
  // });

  const app = createApp(App);

  // 注册Vben提供的v-loading和v-spinning指令
  registerLoadingDirective(app, {
    loading: false, // Vben提供的v-loading指令和Element Plus提供的v-loading指令二选一即可，此处false表示不注册Vben提供的v-loading指令
    spinning: 'spinning',
  });
  setupAntd(app);
  // 国际化 i18n 配置
  await setupI18n(app);

  // 配置 pinia-tore
  await initStores(app, { namespace });

  // setupGlobDirectives(app);

  // 加载字典表
  const dictStore = useDictStore();
  try {
    const res = await getDictListApi();
    dictStore.setDict(res);
  } catch (error) {
    console.error(error);
  }
  // 加载系统配置
  const { systemConfig, setSystemConfig } = useSystemConfigStore();
  try {
    const res = await getPublicConfigInfoApi();
    setSystemConfig(res);
  } catch (error) {
    console.error(error);
  }
  // 安装权限指令
  registerAccessDirective(app);

  // 初始化 tippy
  initTippy(app);

  // 配置路由及路由守卫
  app.use(router);

  // 配置Motion插件
  app.use(MotionPlugin);

  // 动态更新标题
  watchEffect(() => {
    if (preferences.app.dynamicTitle) {
      const routeTitle = router.currentRoute.value.meta?.title;
      const pageTitle = (routeTitle ? `${$t(routeTitle)} - ` : '') + (systemConfig.title || preferences.app.name);
      useTitle(pageTitle);
    }
  });

  // 修改 favicon
  const link = document.querySelector("link[rel='icon']");
  const faviconHref = systemConfig.imgFavicon ?? '/favicon.ico';
  if (link) {
    link.href = faviconHref;
  } else {
    const newLink = document.createElement('link');
    newLink.rel = 'icon';
    newLink.href = faviconHref;
    document.head.append(newLink);
  }

  app.mount('#app');
}

export { bootstrap };
