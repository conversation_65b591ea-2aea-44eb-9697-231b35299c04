<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { FactoringProduct } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Button, message, Space, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delProductApi, getProductPageListApi } from '#/api';

import Form from './form.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'productName',
      label: '采购订单编号',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '采购订单名称',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '所属项目编号',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '所属项目名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务结构',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '关联销售订单编号',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '关联销售订单名称',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '上游企业',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '贸易执行企业',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '担保方',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '完成状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'establishDate',
      label: '业务日期',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '创建人',
    },
    {
      component: 'RangePicker',
      fieldName: 'establishDate',
      label: '创建时间',
    },
  ],
  fieldMappingTime: [['establishDate', ['establishDateStart', 'establishDateEnd'], 'x']],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'productCode', title: '采购订单编号' },
    { field: 'productName', title: '采购订单名称' },
    {
      field: 'productType',
      title: '上游企业',
    },
    {
      field: 'upStatus',
      title: '贸易执行企业',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: '',
        },
      },
    },
    { field: 'createTime', title: '担保方' },
    { field: 'lastUpDate', title: '所属项目名称' },
    { field: 'lastUpDate', title: '所属项目编号' },
    { field: 'lastUpDate', title: '业务结构' },
    { field: 'lastUpDate', title: '项目模式' },
    { field: 'lastUpDate', title: '关联销售订单名称' },
    { field: 'lastUpDate', title: '关联销售订单编号' },
    { field: 'lastUpDate', title: '业务日期' },
    { field: 'lastUpDate', title: '审批状态' },
    { field: 'lastUpDate', title: '业务状态' },
    { field: 'lastUpDate', title: '完成状态' },
    { field: 'lastUpDate', title: '创建时间' },
    { field: 'lastUpDate', title: '创建人' },
    { field: 'lastUpDate', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getProductPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: FactoringProduct) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const del = async (row: FactoringProduct) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delProductApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.Add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="edit(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink @click="edit(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="edit(row)">
            {{ $t('base.invalidate') }}
          </TypographyLink>
          <TypographyLink @click="edit(row)">
            {{ $t('base.copy') }}
          </TypographyLink>
          <TypographyLink @click="edit(row)">
            {{ $t('base.completed') }}
          </TypographyLink>
          <TypographyLink @click="edit(row)">
            {{ $t('base.cancel') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
