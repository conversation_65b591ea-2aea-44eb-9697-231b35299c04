<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { EnterpriseInfo } from '#/api';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

import { addEnterpriseApi, editEnterpriseApi, infoEnterpriseApi } from '#/api';

import AccountInfo from './components/account-info.vue';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();
// 根据接口定义初始化信息
const defaultForm: Partial<EnterpriseInfo> = {
  companyName: '',
  creditCode: '',
  industry: '',
  legalRepresentative: '',
  organCode: '',
  taxpayerNumber: '',
  busRegistrationNumber: '',
  registrationStatus: '',
  establishDate: undefined,
  taxpayerQualification: '',
  businessTermStart: undefined,
  businessTermEnd: undefined,
  principalApprovalTime: undefined,
  companyType: '',
  personnelPhone: '',
  personnelScale: '',
  registeredCapital: 0,
  companyContactEmail: '',
  companyOfficialWebsite: '',
  actualCapital: 0,
  registerOrgan: '',
  financialAccountingNumber: '',
  auxiliaryAccountingCode: '',
  registeredAddress: {},
  registeredAddressAll: '',
  businessAddress: {},
  businessAddressAll: '',
  businessScope: '',
  remarks: '',
  businessRoles: '',
  status: '',
  enableStatus: '',
  accountList: [],
};

const enterpriseInfo = ref<Partial<EnterpriseInfo>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
// 修改4：更新验证规则以匹配Enterprise接口字段
const rules: Record<string, Rule[]> = {
  companyName: [{ required: false, message: '请输入', trigger: 'blur' }],
  creditCode: [{ required: true, message: '请输入', trigger: 'blur' }],
  industry: [{ required: false, message: '请选择', trigger: 'change' }],
  legalRepresentative: [{ required: false, message: '请输入', trigger: 'blur' }],
  organCode: [{ required: false, message: '请输入', trigger: 'blur' }],
  taxpayerNumber: [{ required: false, message: '请输入', trigger: 'blur' }],
  busRegistrationNumber: [{ required: false, message: '请输入', trigger: 'blur' }],
  registrationStatus: [{ required: false, message: '请输入', trigger: 'blur' }],
  establishDate: [{ required: false, message: '请选择', trigger: 'change' }],
  taxpayerQualification: [{ required: false, message: '请输入', trigger: 'blur' }],
  businessTermStart: [{ required: false, message: '请选择', trigger: 'change' }],
  businessTermEnd: [{ required: false, message: '请选择', trigger: 'change' }],
  principalApprovalTime: [{ required: false, message: '请选择', trigger: 'change' }],
  companyType: [{ required: false, message: '请输入', trigger: 'blur' }],
  personnelPhone: [{ required: false, message: '请输入', trigger: 'blur' }],
  personnelScale: [{ required: false, message: '请输入', trigger: 'blur' }],
  registeredCapital: [{ required: false, message: '请输入', trigger: 'blur' }],
  companyContactEmail: [{ required: false, message: '请输入', trigger: 'blur' }],
  companyOfficialWebsite: [{ required: false, message: '请输入', trigger: 'blur' }],
  actualCapital: [{ required: false, message: '请输入', trigger: 'blur' }],
  registerOrgan: [{ required: false, message: '请输入', trigger: 'blur' }],
  financialAccountingNumber: [{ required: false, message: '请输入', trigger: 'blur' }],
  auxiliaryAccountingCode: [{ required: false, message: '请输入', trigger: 'blur' }],
  businessScope: [{ required: false, message: '请输入', trigger: 'blur' }],
  remarks: [{ required: false, message: '请输入备注', trigger: 'blur' }],
};
const title = computed(() => {
  return enterpriseInfo.value.id ? '编辑采购订单' : '新增采购订单';
});
const accountGridRef = ref();

const init = async (data: EnterpriseInfo) => {
  if (data.id) {
    enterpriseInfo.value = await infoEnterpriseApi({ id: data.id as string });
    accountGridRef.value.setAccountData(enterpriseInfo.value.accountList);
  }
};

const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  enterpriseInfo.value.accountList = accountGridRef.value?.getAccountData() || [];
  changeOkLoading(true);
  let api = addEnterpriseApi;
  if (enterpriseInfo.value.id) {
    api = editEnterpriseApi;
  }
  try {
    const res = await api(enterpriseInfo.value as EnterpriseInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="enterpriseInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基础信息 -->
      <BasicCaption content="基础信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="采购订单编号" name="companyName">
            <Input v-model:value="enterpriseInfo.companyName" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="采购订单名称" name="creditCode">
            <Input v-model:value="enterpriseInfo.creditCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="所属项目名称" name="industry">
            <Select v-model:value="enterpriseInfo.industry" :options="getDictList('industry')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="所属项目编号" name="legalRepresentative">
            <Input v-model:value="enterpriseInfo.legalRepresentative" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务结构" name="organCode">
            <Input v-model:value="enterpriseInfo.organCode" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目模式" name="taxpayerNumber">
            <Input v-model:value="enterpriseInfo.taxpayerNumber" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="关联销售订单" name="busRegistrationNumber">
            <Select v-model:value="enterpriseInfo.busRegistrationNumber" :options="getDictList('industry')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="销售订单编号" name="registrationStatus">
            <Input v-model:value="enterpriseInfo.registrationStatus" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="贸易执行企业" name="registrationStatus">
            <Input v-model:value="enterpriseInfo.registrationStatus" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="上游企业" name="busRegistrationNumber">
            <Select v-model:value="enterpriseInfo.busRegistrationNumber" :options="getDictList('industry')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务负责人" name="registrationStatus">
            <Input v-model:value="enterpriseInfo.registrationStatus" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="运营负责人" name="registrationStatus">
            <Input v-model:value="enterpriseInfo.registrationStatus" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务日期" name="establishDate">
            <DatePicker v-model:value="enterpriseInfo.establishDate" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="预计结束日期" name="establishDate">
            <DatePicker v-model:value="enterpriseInfo.establishDate" class="w-full" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="预付款比例(%)" name="taxpayerQualification">
            <Input v-model:value="enterpriseInfo.taxpayerQualification" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem name="businessTerm">
            <template #label>
              预付款金额
              <VbenTooltip title="默认预付款金额=采购订单总金额*预付款比例">
                <VbenIcon icon="ant-design:question-circle-outlined" class="mr-1 cursor-pointer text-base" />
              </VbenTooltip>
            </template>
            <Input v-model:value="enterpriseInfo.taxpayerQualification" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="enterpriseInfo.remarks" :rows="4" />
          </FormItem>
        </Col>
      </Row>

      <BasicCaption content="商品信息" />
      <AccountInfo ref="accountGridRef" />
    </Form>
  </BasicPopup>
</template>
